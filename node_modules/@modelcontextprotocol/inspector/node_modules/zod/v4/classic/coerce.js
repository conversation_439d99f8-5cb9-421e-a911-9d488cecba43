import * as core from "../core/index.js";
import * as schemas from "./schemas.js";
export function string(params) {
    return core._coercedString(schemas.ZodString, params);
}
export function number(params) {
    return core._coercedNumber(schemas.ZodNumber, params);
}
export function boolean(params) {
    return core._coercedBoolean(schemas.ZodBoolean, params);
}
export function bigint(params) {
    return core._coercedBigint(schemas.ZodBigInt, params);
}
export function date(params) {
    return core._coercedDate(schemas.ZodDate, params);
}
