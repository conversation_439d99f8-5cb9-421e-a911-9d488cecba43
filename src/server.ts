import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from 'zod';

const mcpServer = new McpServer({
    name: "cactus_ai",
    version: "1.0.0",
    capabilities: {
        resources: {},
        tools: {},
        prompts: {}
    } 
});

mcpServer.registerTool("create-workout-log",
    {
        title: "create-workout-log",
        description: "create a new workout log in the database",
        inputSchema: {
            user_ref_id: z.string(),
            workout_name: z.string(),
            date: z.date(),
        } 
    },
    async (params) => {
        try {
            console.log("create-workout-log", params);
            return {
                content: [
                    {
                        type: "text",
                        text: "workout log created successfully"
                    }
                ]
            }
        } catch {
            return {
                content: [
                    {
                        type: "text",
                        text: "failed to create workout log"
                    }
                ]
            }
        }
    }
);


// mcpServer.tool("create-workout-log", "create a new workout log in the database" ,{
//     user_ref_id : z.string(),
//     workout_name : z.string(),
//     date : z.date(),
// }, {

//     title : "create workout log",
//     readOnlyHint:false,
//     destructiveHint:false,
//     idempotentHint:false,
//     openWorldHint:true,


// }, async (params) => {
//     try{
//         console.log("create-workout-log", params);
//         return {
//             content : [
//                 {
//                     type : "text",
//                     text : "workout log created successfully"
//                 }
//             ]
//         }
//     }catch{
//         return {
//             content : [
//                 {
//                     type : "text",
//                     text : "failed to create workout log"
//                 }
//             ]
//         }
//     }
// }
// )


export const startMcpServer = async () => {

    const transport = new StdioServerTransport();
    await mcpServer.connect(transport);
    console.log("mcp server started");
}



startMcpServer();